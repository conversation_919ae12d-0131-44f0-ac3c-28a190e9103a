import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:math';

import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';

class GoogleMapsLocationPicker extends StatefulWidget {
  final Function(String address, double latitude, double longitude) onLocationSelected;
  final String? initialAddress;

  const GoogleMapsLocationPicker({
    super.key,
    required this.onLocationSelected,
    this.initialAddress,
  });

  @override
  State<GoogleMapsLocationPicker> createState() => _GoogleMapsLocationPickerState();
}

class _GoogleMapsLocationPickerState extends State<GoogleMapsLocationPicker> {
  GoogleMapController? _mapController;
  LatLng _currentPosition = const LatLng(32.8872, 13.1913); // Tripoli, Libya default
  String _currentAddress = '';
  bool _isLoading = false;
  bool _hasLocationPermission = false;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _checkAndRequestLocationPermission();
  }

  Future<void> _checkAndRequestLocationPermission() async {
    setState(() {
      _isLoading = true;
    });

    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
        setState(() {
          _hasLocationPermission = false;
        });
        print("🔴 Location permissions denied. MyLocation layer will be disabled.");
        // Update to default location (Tripoli, Libya) if no permission
        _updateLocation(_currentPosition);
      } else {
        setState(() {
          _hasLocationPermission = true;
        });
        print("🟢 Location permissions granted. MyLocation layer enabled.");
        // Proceed to get the actual current location only after permission is granted
        await _getCurrentLocation();
      }
    } catch (e) {
      setState(() {
        _hasLocationPermission = false;
      });
      print("🔴 Error checking location permissions: $e");
      _updateLocation(_currentPosition);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    if (!_hasLocationPermission) {
      _updateLocation(_currentPosition);
      return;
    }

    try {
      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      LatLng newPosition = LatLng(position.latitude, position.longitude);
      _updateLocation(newPosition);
    } catch (e) {
      print("🔴 Error getting current location: $e");
      // Use default Libya location if error
      _updateLocation(_currentPosition);
    }
  }

  Future<void> _updateLocation(LatLng position) async {
    setState(() {
      _currentPosition = position;
      _markers = {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
          infoWindow: InfoWindow(
            title: trans('Selected Location'),
            snippet: _currentAddress,
          ),
        ),
      };
    });

    // Move camera to new position
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLng(position),
      );
    }

    // Get address from coordinates
    await _getAddressFromCoordinates(position);
  }

  Future<void> _getAddressFromCoordinates(LatLng position) async {
    try {
      print('🗺️ Getting address for coordinates: ${position.latitude}, ${position.longitude}');

      // CRITICAL FIX: Check if coordinates are in Libya first
      if (_isLocationInLibya(position)) {
        print('🇱🇾 Coordinates are in Libya - using Libya-specific address generation');
        String libyaAddress = await _generateLibyaAddress(position);

        if (mounted) {
          setState(() {
            _currentAddress = libyaAddress;
          });
        }

        print('🗺️ Libya address generated: $libyaAddress');
        return;
      }

      // For non-Libya coordinates, use standard reverse geocoding
      print('🌍 Coordinates outside Libya - using standard reverse geocoding');

      // Add timeout to prevent hanging
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      ).timeout(Duration(seconds: 10));

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '';

        print('🗺️ Placemark found: ${place.toString()}');

        if (place.street != null && place.street!.isNotEmpty) {
          address += '${place.street}, ';
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          address += '${place.locality}, ';
        }
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          address += '${place.administrativeArea}, ';
        }
        if (place.country != null && place.country!.isNotEmpty) {
          address += place.country!;
        }

        // Remove trailing comma and space
        address = address.replaceAll(RegExp(r', $'), '');

        if (mounted) {
          setState(() {
            _currentAddress = address.isNotEmpty ? address : 'موقع محدد على الخريطة';
          });
        }

        print('🗺️ Address resolved: $address');
      } else {
        print('⚠️ No placemarks found for coordinates');
        if (mounted) {
          setState(() {
            _currentAddress = 'موقع محدد على الخريطة';
          });
        }
      }
    } catch (e) {
      print('❌ Error getting address: $e');
      if (mounted) {
        setState(() {
          _currentAddress = 'موقع محدد على الخريطة';
        });
      }
    }
  }

  void _onMapTapped(LatLng position) {
    _updateLocation(position);
  }

  /// Check if coordinates are within Libya's geographical boundaries
  bool _isLocationInLibya(LatLng position) {
    // Libya's approximate geographical boundaries
    // North: 33.2°N, South: 19.5°N, East: 25.2°E, West: 9.3°E
    double lat = position.latitude;
    double lng = position.longitude;

    bool isInLibya = lat >= 19.5 && lat <= 33.2 && lng >= 9.3 && lng <= 25.2;

    print('🗺️ Coordinate check: lat=$lat, lng=$lng, isInLibya=$isInLibya');
    return isInLibya;
  }

  /// Generate a proper Libya address based on coordinates
  Future<String> _generateLibyaAddress(LatLng position) async {
    try {
      // Find the nearest Libyan city based on coordinates
      String nearestCity = _findNearestLibyanCity(position);

      // Generate a proper Libya address format
      String address = 'موقع محدد على الخريطة، $nearestCity، ليبيا';

      print('🇱🇾 Generated Libya address: $address');
      return address;

    } catch (e) {
      print('❌ Error generating Libya address: $e');
      // Fallback to generic Libya address
      return 'موقع محدد على الخريطة، ليبيا';
    }
  }

  /// Find the nearest Libyan city based on coordinates
  String _findNearestLibyanCity(LatLng position) {
    // Major Libyan cities with approximate coordinates
    Map<String, LatLng> libyanCities = {
      'طرابلس': LatLng(32.8872, 13.1913),
      'بنغازي': LatLng(32.1165, 20.0686),
      'مصراتة': LatLng(32.3743, 15.0919),
      'الزاوية': LatLng(32.7569, 12.7277),
      'بيان': LatLng(32.7617, 13.0200),
      'صبراتة': LatLng(32.7931, 12.4844),
      'زليتن': LatLng(32.4674, 14.5687),
      'الخمس': LatLng(32.6486, 14.2618),
      'سرت': LatLng(31.2089, 16.5887),
      'أجدابيا': LatLng(30.7554, 20.2263),
    };

    String nearestCity = 'طرابلس'; // Default to Tripoli
    double minDistance = double.infinity;

    libyanCities.forEach((cityName, cityCoords) {
      double distance = _calculateDistance(position, cityCoords);
      if (distance < minDistance) {
        minDistance = distance;
        nearestCity = cityName;
      }
    });

    print('🗺️ Nearest city to ${position.latitude}, ${position.longitude}: $nearestCity (${minDistance.toStringAsFixed(2)} km)');
    return nearestCity;
  }

  /// Calculate distance between two coordinates in kilometers
  double _calculateDistance(LatLng pos1, LatLng pos2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double lat1Rad = pos1.latitude * (3.14159 / 180);
    double lat2Rad = pos2.latitude * (3.14159 / 180);
    double deltaLatRad = (pos2.latitude - pos1.latitude) * (3.14159 / 180);
    double deltaLngRad = (pos2.longitude - pos1.longitude) * (3.14159 / 180);

    double a = (sin(deltaLatRad / 2) * sin(deltaLatRad / 2)) +
        (cos(lat1Rad) * cos(lat2Rad) * sin(deltaLngRad / 2) * sin(deltaLngRad / 2));
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  void _confirmLocation() {
    try {
      // Ensure we have a valid address before confirming
      String finalAddress = _currentAddress.isNotEmpty ? _currentAddress : 'موقع محدد على الخريطة';

      print('🗺️ Confirming location: $finalAddress');
      print('🗺️ Coordinates: ${_currentPosition.latitude}, ${_currentPosition.longitude}');

      widget.onLocationSelected(
        finalAddress,
        _currentPosition.latitude,
        _currentPosition.longitude,
      );

      // Use a safer navigation method
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      print('❌ Error confirming location: $e');
      // Still try to navigate back
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  void dispose() {
    // Dispose of the map controller to prevent memory leaks
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeAreaWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text(trans('Select Location')),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 1,
        ),
        body: Stack(
          children: [
            GoogleMap(
              onMapCreated: (GoogleMapController controller) {
                _mapController = controller;
                print('🗺️ Google Maps controller created successfully');
              },
              initialCameraPosition: CameraPosition(
                target: _currentPosition,
                zoom: 15.0,
              ),
              onTap: _onMapTapped,
              markers: _markers,
              myLocationEnabled: _hasLocationPermission,
              myLocationButtonEnabled: _hasLocationPermission,
              mapType: MapType.normal,
              // Performance optimizations to prevent JNI issues
              compassEnabled: true,
              mapToolbarEnabled: false,
              zoomControlsEnabled: false,
              zoomGesturesEnabled: true,
              scrollGesturesEnabled: true,
              tiltGesturesEnabled: true,
              rotateGesturesEnabled: true,
              // Reduce map complexity to prevent performance issues
              buildingsEnabled: false,
              trafficEnabled: false,
            ),
            if (_isLoading)
              Container(
                color: Colors.black26,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      trans('Selected Address'),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _currentAddress.isNotEmpty ? _currentAddress : trans('Tap on map to select location'),
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: SecondaryButton(
                            title: trans('Cancel'),
                            action: () => Navigator.of(context).pop(),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: PrimaryButton(
                            title: trans('Confirm Location'),
                            action: _currentAddress.isNotEmpty ? _confirmLocation : null,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
