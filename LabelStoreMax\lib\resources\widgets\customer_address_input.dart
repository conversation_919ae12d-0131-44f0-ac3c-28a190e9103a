//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/customer_country.dart';
import '/app/models/libyan_city.dart';

import '/resources/widgets/velvete_ui.dart';
import '/resources/widgets/google_maps_location_picker.dart';

import 'package:nylo_framework/nylo_framework.dart';

class CustomerAddressInput extends StatefulWidget {
  const CustomerAddressInput(
      {super.key,
      required this.txtControllerFirstName,
      required this.txtControllerLastName,
      required this.txtControllerAddressLine,
      required this.txtControllerCity,
      required this.txtControllerPostalCode,
      this.txtControllerEmailAddress,
      this.txtControllerPhoneN<PERSON>,
      required this.customerCountry,
      required this.onTapCountry});

  final TextEditingController? txtControllerFirstName,
      txtControllerLastName,
      txtControllerAddressLine,
      txtControllerCity,
      txtControllerPostalCode,
      txtControllerEmailAddress,
      txtControllerPhoneNumber;

  final CustomerCountry? customerCountry;
  final Function() onTapCountry;

  @override
  State<CustomerAddressInput> createState() => _CustomerAddressInputState();
}

class _CustomerAddressInputState extends State<CustomerAddressInput> {
  LibyanCity? _selectedCity;
  List<LibyanCity> _cities = [];

  @override
  void initState() {
    super.initState();

    // CRITICAL FIX: Ensure unique cities to prevent DropdownButton duplicate value error
    List<LibyanCity> allCities = LibyanCitiesData.getAllCities();
    _cities = allCities.toSet().toList(); // Remove duplicates using Set

    print('🔍 Loaded ${allCities.length} total cities, ${_cities.length} unique cities');

    // Try to find the selected city from the current text controller value
    if (widget.txtControllerCity?.text.isNotEmpty == true) {
      _selectedCity = LibyanCitiesData.findCityByName(widget.txtControllerCity!.text) ??
                     LibyanCitiesData.findCityByArabicName(widget.txtControllerCity!.text);
      print('🔍 Pre-selected city: ${_selectedCity?.getDisplayName()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Name Fields Row - Consistent Spacing
          Row(
            children: [
              Expanded(
                child: TextEditingRow(
                  heading: "الاسم",
                  controller: widget.txtControllerFirstName,
                  shouldAutoFocus: true,
                ),
              ),
              SizedBox(width: 16), // Consistent spacing
              Expanded(
                child: TextEditingRow(
                  heading: "اللقب",
                  controller: widget.txtControllerLastName,
                ),
              ),
            ],
          ),

          SizedBox(height: 20), // Consistent vertical spacing
        // Enhanced Google Maps Address Selection
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              trans("Address Line"),
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            SizedBox(height: 8),

            // Expanded Map Button (replaces Row layout)
            _buildEnhancedMapButton(),
          ],
        ),
        // Libyan Cities Dropdown
        Container(
          margin: EdgeInsets.symmetric(vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "المدينة", // Arabic for "City"
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<LibyanCity>(
                    value: _selectedCity,
                    hint: Text("اختر المدينة"), // Arabic for "Choose City"
                    isExpanded: true,
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    items: _cities.map((LibyanCity city) {
                      return DropdownMenuItem<LibyanCity>(
                        value: city,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(city.getDisplayName()),
                            Text(
                              city.getFormattedDeliveryCost(),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (LibyanCity? newValue) {
                      setState(() {
                        _selectedCity = newValue;
                        // Update the text controller with the Arabic city name for WooCommerce API
                        widget.txtControllerCity?.text = newValue?.nameArabic ?? newValue?.name ?? '';
                        print('✅ Selected city: ${newValue?.nameArabic} (${newValue?.deliveryCost} LYD)');
                      });
                    },
                  ),
                ),
              ),
              if (_selectedCity != null)
                Container(
                  margin: EdgeInsets.only(top: 4),
                  child: Text(
                    "تكلفة التوصيل: ${_selectedCity!.getFormattedDeliveryCost()}", // Arabic for "Delivery cost"
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
          // Email Field
          if (widget.txtControllerEmailAddress != null) ...[
            TextEditingRow(
              heading: "البريد الإلكتروني",
              keyboardType: TextInputType.emailAddress,
              controller: widget.txtControllerEmailAddress,
            ),
            SizedBox(height: 20),
          ],
          // Phone Field
          if (widget.txtControllerPhoneNumber != null) ...[
            LibyanPhoneInputWidget(
              heading: "Phone Number",
              controller: widget.txtControllerPhoneNumber!,
            ),
            SizedBox(height: 20),
          ],
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              if (widget.customerCountry?.hasState() ?? false) ...[
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 23,
                        width: double.infinity,
                        child: Text(
                          trans("State"),
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.location_city, color: Colors.grey[600], size: 20),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  (widget.customerCountry!.state != null
                                      ? (widget.customerCountry?.state?.name ?? "")
                                      : trans("No state required")),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                              Icon(Icons.lock, color: Colors.grey[500], size: 16),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 8),
              ],
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 23,
                      width: double.infinity,
                      child: Text(
                        "البلد",
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            _buildLibyanFlag(),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                "Libya", // Hardcoded Libya
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                            Icon(Icons.lock, color: Colors.grey[500], size: 16),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    ),
    );
  }

  /// Build enhanced map button with visual feedback
  Widget _buildEnhancedMapButton() {
    bool hasAddress = widget.txtControllerAddressLine?.text.isNotEmpty == true;

    return Container(
      width: double.infinity,
      height: 60,
      child: ElevatedButton(
        onPressed: () => _openMapPicker(),
        style: ElevatedButton.styleFrom(
          backgroundColor: hasAddress ? Color(0xFF4CAF50) : Color(0xFFB76E79),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: hasAddress ? 6 : 4,
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              hasAddress ? Icons.location_on : Icons.map_outlined,
              size: 24,
            ),
            SizedBox(width: 12),
            Flexible(
              child: Text(
                _getDisplayText(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            if (hasAddress) ...[
              SizedBox(width: 8),
              Icon(Icons.edit, size: 16),
            ],
          ],
        ),
      ),
    );
  }

  /// Get display text for the map button
  String _getDisplayText() {
    if (widget.txtControllerAddressLine?.text.isNotEmpty == true) {
      return widget.txtControllerAddressLine!.text;
    }
    return 'اضغط لتحديد العنوان من الخريطة';
  }

  /// Open map picker with enhanced navigation
  void _openMapPicker() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GoogleMapsLocationPicker(
          initialAddress: widget.txtControllerAddressLine?.text,
          onLocationSelected: (address, lat, lng) {
            // CRITICAL FIX: Ensure proper Libya address formatting
            String finalAddress = _validateAndFormatAddress(address, lat, lng);
            widget.txtControllerAddressLine?.text = finalAddress;

            // Store coordinates in NyStorage for later retrieval
            _storeCoordinates(lat, lng);

            // Also store the validated address for verification
            _storeValidatedAddress(finalAddress);

            setState(() {}); // Refresh button appearance
          },
        ),
      ),
    );
  }

  /// Validate and format the address to ensure it's correct for Libya
  String _validateAndFormatAddress(String address, double lat, double lng) {
    print('🔍 Validating address: $address for coordinates: $lat, $lng');

    // Check if coordinates are in Libya (same bounds as in GoogleMapsLocationPicker)
    bool isInLibya = lat >= 19.5 && lat <= 33.2 && lng >= 9.3 && lng <= 25.2;

    if (isInLibya) {
      // If the address contains non-Arabic text or suspicious patterns, replace it
      if (address.contains('California') ||
          address.contains('United States') ||
          address.contains('USA') ||
          !address.contains('ليبيا')) {

        print('⚠️ Detected invalid address for Libya coordinates, generating proper address');
        return 'موقع محدد على الخريطة، ليبيا';
      }
    }

    print('✅ Address validation passed: $address');
    return address;
  }

  /// Store validated address for debugging and verification
  void _storeValidatedAddress(String address) async {
    try {
      await NyStorage.save('validated_address', address);
      print('🗺️ Stored validated address: $address');
    } catch (e) {
      print('❌ Error storing validated address: $e');
    }
  }

  /// Store coordinates in local storage for later use in order creation
  void _storeCoordinates(double lat, double lng) async {
    try {
      await NyStorage.save('selected_location_lat', lat.toString());
      await NyStorage.save('selected_location_lng', lng.toString());
      print('🗺️ Stored coordinates in NyStorage: lat=$lat, lng=$lng');
    } catch (e) {
      print('❌ Error storing coordinates: $e');
    }
  }

  /// Build custom Libyan flag widget with official colors
  Widget _buildLibyanFlag() {
    return Container(
      width: 24,
      height: 16,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        border: Border.all(color: Colors.grey[300]!, width: 0.5),
      ),
      child: Column(
        children: [
          // Red stripe
          Expanded(
            child: Container(
              width: double.infinity,
              color: Color(0xFFE70013), // Official Libyan red
            ),
          ),
          // Black stripe
          Expanded(
            child: Container(
              width: double.infinity,
              color: Color(0xFF000000), // Black
            ),
          ),
          // Green stripe
          Expanded(
            child: Container(
              width: double.infinity,
              color: Color(0xFF239F40), // Official Libyan green
            ),
          ),
        ],
      ),
    );
  }
}

class LibyanPhoneInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final String heading;
  const LibyanPhoneInputWidget({
    Key? key,
    required this.controller,
    required this.heading,
  }) : super(key: key);

  @override
  State<LibyanPhoneInputWidget> createState() =>
      _LibyanPhoneInputWidgetState();
}

class _LibyanPhoneInputWidgetState extends State<LibyanPhoneInputWidget> {
  final TextEditingController _displayController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeFromController();
    _displayController.addListener(_onDisplayChanged);
  }

  void _initializeFromController() {
    String currentValue = widget.controller.text;
    if (currentValue.isNotEmpty) {
      String digits = currentValue.replaceAll(RegExp(r'[^\d]'), '');
      if (digits.startsWith('218')) {
        digits = digits.substring(3);
      }
      _displayController.text = digits;
    }
  }

  void _onDisplayChanged() {
    String input = _displayController.text;
    String digits = input.replaceAll(RegExp(r'[^\d]'), '');
    digits = digits.replaceFirst(RegExp(r'^0+'), '');
    if (digits.startsWith('218')) {
      digits = digits.substring(3);
    }
    if (digits.length > 9) {
      digits = digits.substring(0, 9);
    }
    widget.controller.text = '+218' + digits;
  }

  @override
  void dispose() {
    _displayController.removeListener(_onDisplayChanged);
    _displayController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _displayController,
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
        labelText: trans(widget.heading),
        prefixText: '+218',
      ),
    );
  }
}
