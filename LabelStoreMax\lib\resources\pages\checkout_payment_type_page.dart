//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/payment_type.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/safearea_widget.dart';

import 'package:nylo_framework/nylo_framework.dart';

class CheckoutPaymentTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-payment-type", (_) => CheckoutPaymentTypePage());

  CheckoutPaymentTypePage({super.key})
      : super(child: () => _CheckoutPaymentTypePageState());
}

class _CheckoutPaymentTypePageState extends NyPage<CheckoutPaymentTypePage> {
  List<PaymentType?> _paymentTypes = [];

  @override
  get init => () async {
        super.init();

        _paymentTypes = await getPaymentTypes();

        if (_paymentTypes.isEmpty &&
            getEnv('APP_DEBUG', defaultValue: false) == true) {
          NyLogger.info(
              'You have no payment methods set. Visit the WooSignal dashboard (https://woosignal.com/dashboard) to set a payment method.');
        }

        if (CheckoutSession.getInstance.paymentType == null) {
          if (_paymentTypes.isNotEmpty) {
            CheckoutSession.getInstance.paymentType = _paymentTypes.firstWhere(
                (paymentType) => paymentType?.id == 20,
                orElse: () => _paymentTypes.first);
          }
        }
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          "طريقة الدفع",
          style: TextStyle(
            color: Color(0xFF2E3A59),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Color(0xFF2E3A59)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeAreaWidget(
        child: _buildBeautifulPaymentPage(),
      ),
    );
  }

  /// Build the beautiful payment page matching the example image
  Widget _buildBeautifulPaymentPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          // Top illustration section
          Container(
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(bottom: 30),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Cash on delivery illustration
                Container(
                  height: 140,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.payments_outlined,
                      size: 50,
                      color: Color(0xFFB76E79),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                // Content section
                Text(
                  'اختر طريقة الدفع',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E3A59),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'حدد الطريقة المناسبة لك',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Payment method card
          _buildPaymentMethodCard(),

          SizedBox(height: 30),

          // Cancel button
          _buildCancelButton(),
        ],
      ),
    );
  }



  /// Build the payment method card matching the example design
  Widget _buildPaymentMethodCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: _paymentTypes.isEmpty ? _buildNoPaymentMethods() : _buildPaymentMethodsList(),
    );
  }

  /// Build no payment methods message
  Widget _buildNoPaymentMethods() {
    return Column(
      children: [
        // Info icon
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Color(0xFFF4C2C2).withValues(alpha: 0.3),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.payment,
            color: Color(0xFFB76E79),
            size: 30,
          ),
        ),
        SizedBox(height: 20),

        // Message
        Text(
          "لا توجد طرق دفع متاحة",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2E3A59),
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),

        Text(
          "No payment methods are available",
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build payment methods list
  Widget _buildPaymentMethodsList() {
    return Column(
      children: [
        // Green checkmark
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Color(0xFF4CAF50),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check,
            color: Colors.white,
            size: 30,
          ),
        ),
        SizedBox(height: 20),

        // Cash on delivery title
        Text(
          "الدفع كاش عند الاستلام",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2E3A59),
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),

        // Detailed description
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Color(0xFFE0E0E0)),
          ),
          child: Text(
            "نفخر بسري بالطاقة لكن المندوب\nمش حيكون معاه ماكينة الدفع\nPOS ، المعاملة تكون أونلاين من\nهاتفك \"تحويل\" يعني البطاقة لازم\nتكون مفعلة عالشراء أونلاين",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: 20),

        // Payment methods selection
        ..._paymentTypes.map((paymentType) => _buildPaymentMethodItem(paymentType!)).toList(),
      ],
    );
  }

  /// Build individual payment method item
  Widget _buildPaymentMethodItem(PaymentType paymentType) {
    bool isSelected = CheckoutSession.getInstance.paymentType?.id == paymentType.id;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isSelected ? Color(0xFF4CAF50).withValues(alpha: 0.1) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? Color(0xFF4CAF50) : Color(0xFFE0E0E0),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          padding: EdgeInsets.all(8),
          child: paymentType.assetImage.startsWith('http')
            ? Image.network(
                paymentType.assetImage,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Icon(Icons.payment, color: Color(0xFFB76E79)),
              )
            : Image.asset(
                getImageAsset(paymentType.assetImage),
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Icon(Icons.payment, color: Color(0xFFB76E79)),
              ),
        ),
        title: Text(
          paymentType.desc,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2E3A59),
          ),
        ),
        trailing: isSelected
          ? Icon(Icons.check_circle, color: Color(0xFF4CAF50), size: 24)
          : Icon(Icons.radio_button_unchecked, color: Color(0xFFCCCCCC), size: 24),
        onTap: () {
          CheckoutSession.getInstance.paymentType = paymentType;
          Navigator.pop(context);
        },
      ),
    );
  }

  /// Build cancel button
  Widget _buildCancelButton() {
    return Container(
      width: double.infinity,
      height: 50,
      child: OutlinedButton(
        onPressed: () => Navigator.pop(context),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: Color(0xFFB76E79), width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          "إلغاء",
          style: TextStyle(
            color: Color(0xFFB76E79),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
