//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/checkout_confirmation_page.dart';
import '/resources/pages/checkout_shipping_type_page.dart';
import '/app/models/checkout_session.dart';
import '/app/models/customer_address.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/bootstrap/app_helper.dart';
// import 'package:woosignal/models/response/woosignal_app.dart'; // Replaced with AppConfig

class CheckoutShippingTypeWidget extends StatelessWidget {
  const CheckoutShippingTypeWidget(
      {super.key,
      required this.context,
      // required this.wooSignalApp, // Replaced with AppConfig
      required this.checkoutSess<PERSON>,
      this.resetState});

  final CheckoutSession checkoutSession;
  final BuildContext context;
  final Function? resetState;
  // final WooSignalApp? wooSignalApp; // Replaced with AppConfig

  @override
  Widget build(BuildContext context) {
    // Check shipping configuration from AppConfig
    bool hasDisableShipping = AppHelper.instance.appConfig?.disableShipping ?? false;
    if (hasDisableShipping == true) {
      return SizedBox.shrink();
    }
    bool hasSelectedShippingType = checkoutSession.shippingType != null;
    return CheckoutRowLine(
      heading: trans(
          hasSelectedShippingType ? "Shipping selected" : "Select shipping"),
      leadImage: Image.asset(
        'public/assets/images/shipping.png', // FIX: Use local asset
        width: 24,
        height: 24,
        errorBuilder: (context, error, stackTrace) => Icon(Icons.local_shipping),
      ),
      leadTitle: hasSelectedShippingType
          ? checkoutSession.shippingType!.getTitle()
          : trans("Select a shipping option"),
      action: _actionSelectShipping,
      showBorderBottom: true,
    );
  }

  _actionSelectShipping() {
    CustomerAddress? shippingAddress =
        checkoutSession.billingDetails?.shippingAddress;
    if (shippingAddress == null || shippingAddress.customerCountry == null) {
      showToastNotification(
        context,
        title: trans("Oops"),
        description: trans("Add your shipping details first"),
        icon: Icons.local_shipping,
      );
      return;
    }
    routeTo(CheckoutShippingTypePage.path, onPop: (value) {
      StateAction.refreshPage(CheckoutConfirmationPage.path, setState: () {});
    });
  }
}
