// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'package:flutter/material.dart';

import '/resources/pages/cart_page.dart';
import '/resources/pages/categories_page.dart';
import '/resources/pages/auth_options_page.dart';
import '/resources/pages/settings_page.dart';
import '/resources/widgets/account_detail_orders_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';


class HomeDrawerWidget extends StatefulWidget {
  const HomeDrawerWidget({super.key});

  @override
  createState() => _HomeDrawerWidgetState();
}

class _HomeDrawerWidgetState extends State<HomeDrawerWidget> {

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFA03247), // #A03247
              Color(0xFFB24056), // #B24056
            ],
          ),
        ),
        child: ListView(
          padding: EdgeInsets.zero,
          children: <Widget>[
            DrawerHeader(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFA03247), // #A03247
                    Color(0xFFB24056), // #B24056
                  ],
                ),
              ),
              child: Center(
                child: Text(
                  'Velvete Store',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            // Arabic Navigation Menu - Elegant Line Art Icons
            _buildMenuTile(
              icon: Icons.home_outlined,
              title: 'الرئيسية',
              onTap: () {
                Navigator.pop(context);
                // Navigate to home - this will be handled by the main navigation
              },
            ),
            _buildMenuTile(
              icon: Icons.dashboard_outlined,
              title: 'الفئات',
              onTap: () {
                Navigator.pop(context);
                routeTo(CategoriesPage.path);
              },
            ),
            _buildMenuTile(
              icon: Icons.shopping_bag_outlined,
              title: 'السلة',
              onTap: () {
                Navigator.pop(context);
                routeTo(CartPage.path);
              },
            ),
            _buildMenuTile(
              icon: Icons.receipt_long_outlined,
              title: 'طلباتي',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => Scaffold(
                      appBar: AppBar(
                        title: Text('طلباتي'),
                        leading: IconButton(
                          icon: Icon(Icons.arrow_back_ios_rounded),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),
                      body: SafeArea(
                        child: AccountDetailOrdersWidget(),
                      ),
                    ),
                  ),
                );
              },
            ),
            _buildMenuTile(
              icon: Icons.tune_outlined,
              title: 'الإعدادات',
              onTap: () {
                Navigator.pop(context);
                routeTo(SettingsPage.path);
              },
            ),
            _buildMenuTile(
              icon: Icons.login_outlined,
              title: 'تسجيل الدخول',
              onTap: () {
                Navigator.pop(context);
                routeTo(AuthOptionsPage.path);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colors.white,
        size: 24,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 24, vertical: 4),
    );
  }
}
