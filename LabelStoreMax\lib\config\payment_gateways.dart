import '/app/models/payment_type.dart';
import '/app/providers/payments/cash_on_delivery.dart';
import '/app/services/dynamic_payment_service.dart';
import '/bootstrap/helpers.dart';
import 'package:nylo_framework/nylo_framework.dart';

/* PAYMENT GATEWAYS
|--------------------------------------------------------------------------
| Configure which payment gateways you want to use.
| Docs here: https://woosignal.com/docs/app/label-storemax
|-------------------------------------------------------------------------- */

const appPaymentGateways = [];
// Available: "Stripe", "CashOnDelivery", "PayPal", "RazorPay"
// e.g. appPaymentGateways = ["Stripe", "CashOnDelivery"]; will only use Stripe and Cash on Delivery.

List<PaymentType> paymentTypeList = [
  addPayment(
    id: 2,
    name: "CashOnDelivery",
    description: trans("Cash on delivery"),
    assetImage: "https://velvete.ly/wp-content/uploads/2025/07/cash.png",
    pay: cashOnDeliveryPay,
  ),

  // e.g. add more here

  // addPayment(
  //   id: 6,
  //   name: "MyNewPaymentMethod",
  //   description: "Debit or Credit Card",
  //   assetImage: "add icon image to public/images/myimage.png",
  //   pay: "myCustomPaymentFunction",
  // ),
];

/// Get dynamic payment gateways from WooCommerce API
/// This replaces the static paymentTypeList with live data from the website
Future<List<PaymentType>> getDynamicPaymentGateways() async {
  final dynamicPaymentService = DynamicPaymentService();

  try {
    // Fetch payment gateways from WooCommerce API
    final dynamicGateways = await dynamicPaymentService.getPaymentGateways();

    if (dynamicGateways.isNotEmpty) {
      print('✅ Using dynamic payment gateways: ${dynamicGateways.length} gateways');
      return dynamicGateways;
    } else {
      print('⚠️ No dynamic payment gateways found, falling back to static list');
      return _getFilteredStaticPaymentTypes();
    }
  } catch (e) {
    print('❌ Error fetching dynamic payment gateways: $e');
    print('⚠️ Falling back to static payment types');
    return _getFilteredStaticPaymentTypes();
  }
}

/// Get filtered static payment types based on appPaymentGateways configuration
List<PaymentType> _getFilteredStaticPaymentTypes() {
  if (appPaymentGateways.isEmpty) {
    return paymentTypeList;
  }

  return paymentTypeList.where((paymentType) {
    return appPaymentGateways.contains(paymentType.name);
  }).toList();
}
