package com.velvete.ly

import android.app.Application
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger

class MainApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // Initialize Facebook SDK before any plugin registration
        try {
            FacebookSdk.sdkInitialize(applicationContext)
            AppEventsLogger.activateApp(this)
            println("✅ Facebook SDK initialized successfully")
        } catch (e: Exception) {
            println("⚠️ Facebook SDK initialization failed: ${e.message}")
            // Continue app initialization even if Facebook SDK fails
        }
    }
}
